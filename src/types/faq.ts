import { faqCategoryTable, faqTable } from "@/lib/db";

export type FaqType = typeof faqTable.$inferSelect;

export type FaqCategoryType = typeof faqCategoryTable.$inferSelect;

export interface FaqFormData {
  question: string;
  answer: string; // This will store JSON string from Tiptap editor
  categoryId?: number;
}

// Helper type for working with parsed Tiptap content
export interface FaqRichTextContent {
  type: string;
  content?: FaqRichTextContent[];
  text?: string;
  marks?: Array<{
    type: string;
    attrs?: Record<string, any>;
  }>;
  attrs?: Record<string, any>;
}
