import { faqTable } from "@/lib/db";
import { executeQuery } from "@/lib/db-manager";
import { desc, eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get("projectId");
    const { id } = await params;

    if (!projectId) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少项目ID参数",
        },
        { status: 400 }
      );
    }

    // 使用executeQuery统一处理数据库连接和查询
    const [category] = await executeQuery(projectId, async (businessDb) => {
      return await businessDb
        .select()
        .from(faqTable)
        .where(eq(faqTable.id, parseInt(id)))
        .orderBy(desc(faqTable.createdAt))
        .limit(1);
    });

    return NextResponse.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error("Get faq error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "获取FAQ失败",
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get("projectId");
    const { id } = await params;
    const body = await request.json();
    const { question, answer, categoryId } = body;

    if (!projectId) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少项目ID参数",
        },
        { status: 400 }
      );
    }

    // 使用executeQuery统一处理数据库连接和查询
    const [category] = await executeQuery(projectId, async (businessDb) => {
      return await businessDb
        .update(faqTable)
        .set({
          question,
          answer,
          categoryId,
        })
        .where(eq(faqTable.id, parseInt(id)))
        .returning();
    });

    return NextResponse.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error("Get faq error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "更新FAQ失败",
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get("projectId");
    const { id } = await params;

    if (!projectId) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少项目ID参数",
        },
        { status: 400 }
      );
    }

    // 使用executeQuery统一处理数据库连接和查询
    const [deletedFaq] = await executeQuery(projectId, async (businessDb) => {
      return await businessDb
        .delete(faqTable)
        .where(eq(faqTable.id, parseInt(id)))
        .returning();
    });

    if (!deletedFaq) {
      return NextResponse.json(
        {
          success: false,
          error: "FAQ不存在",
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: deletedFaq,
    });
  } catch (error) {
    console.error("Delete faq error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "删除FAQ失败",
      },
      { status: 500 }
    );
  }
}
