import {
  pgTable,
  serial,
  varchar,
  text,
  timestamp,
  boolean,
  integer,
  jsonb,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";

//meta
//cdn 链接 bucket
//sysconfig
//h 标签

// TDK (Title, Description, Keywords) 页面SEO配置表
export const pageMeta = pgTable("page_meta", {
  id: serial("id").primaryKey(),
  path: varchar("path", { length: 500 }).unique().notNull(), // 页面路径
  title: varchar("title", { length: 255 }),
  description: text("description"),
  keywords: varchar("keywords", { length: 500 }),
  language: varchar("language", { length: 10 }).default("en"),
  isActive: boolean("is_active").default(true),
  jsonLd: jsonb("json_ld"),
  openGraph: jsonb("open_graph"),
  canonical: varchar("canonical", { length: 500 }),
  noIndex: boolean("no_index").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// 博客分类表
export const blogCategories = pgTable("blog_categories", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 100 }).notNull(),
  slug: varchar("slug", { length: 100 }).unique().notNull(),
  description: text("description"),
  sort: integer("sort").default(0),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// 博客标签表
export const blogTags = pgTable("blog_tags", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 50 }).unique().notNull(),
  color: varchar("color", { length: 7 }).default("#3b82f6"), // 十六进制颜色
  count: integer("count").default(0), // 使用次数
  createdAt: timestamp("created_at").defaultNow(),
});

// 博客文章表
export const blogPosts = pgTable("blog_posts", {
  id: serial("id").primaryKey(),
  title: varchar("title", { length: 255 }).notNull(),
  slug: varchar("slug", { length: 255 }).unique().notNull(),
  summary: text("summary"), // 摘要
  content: text("content"), // 文章内容（Markdown格式）
  thumbnail: text("thumbnail"), // 缩略图
  categoryId: integer("category_id").references(() => blogCategories.id),
  status: varchar("status", { length: 20 }).default("draft"), // draft, published, archived
  isTop: boolean("is_top").default(false), // 是否置顶
  viewCount: integer("view_count").default(0),
  likeCount: integer("like_count").default(0),
  publishedAt: timestamp("published_at"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
  authorName: varchar("author_name", { length: 100 }),
  authorEmail: varchar("author_email", { length: 255 }),
  // SEO相关字段
  metaTitle: varchar("meta_title", { length: 255 }),
  metaDescription: text("meta_description"),
  metaKeywords: varchar("meta_keywords", { length: 500 }),
});

// 博客文章标签关联表
export const blogPostTags = pgTable("blog_post_tags", {
  id: serial("id").primaryKey(),
  postId: integer("post_id").references(() => blogPosts.id, {
    onDelete: "cascade",
  }),
  tagId: integer("tag_id").references(() => blogTags.id, {
    onDelete: "cascade",
  }),
  createdAt: timestamp("created_at").defaultNow(),
});

// 站点配置表
export const siteConfig = pgTable("site_config", {
  id: serial("id").primaryKey(),
  key: varchar("key", { length: 255 }).unique().notNull(),
  value: text("value"),
  type: varchar("type", { length: 50 }).default("string"), // string, number, boolean, json
  category: varchar("category", { length: 100 }).default("general"), // 配置分类
  description: text("description"),
  isPublic: boolean("is_public").default(false), // 是否可以在前端访问
  updatedAt: timestamp("updated_at").defaultNow(),
});

// 页面访问统计表
export const pageStats = pgTable("page_stats", {
  id: serial("id").primaryKey(),
  path: varchar("path", { length: 500 }).notNull(),
  ip: varchar("ip", { length: 45 }),
  userAgent: text("user_agent"),
  referer: text("referer"),
  country: varchar("country", { length: 100 }),
  city: varchar("city", { length: 100 }),
  device: varchar("device", { length: 50 }), // mobile, desktop, tablet
  browser: varchar("browser", { length: 50 }),
  os: varchar("os", { length: 50 }),
  visitedAt: timestamp("visited_at").defaultNow(),
});

// 关系定义
export const blogCategoriesRelations = relations(
  blogCategories,
  ({ many }) => ({
    posts: many(blogPosts),
  })
);

export const blogTagsRelations = relations(blogTags, ({ many }) => ({
  postTags: many(blogPostTags),
}));

export const blogPostsRelations = relations(blogPosts, ({ one, many }) => ({
  category: one(blogCategories, {
    fields: [blogPosts.categoryId],
    references: [blogCategories.id],
  }),
  postTags: many(blogPostTags),
}));

export const blogPostTagsRelations = relations(blogPostTags, ({ one }) => ({
  post: one(blogPosts, {
    fields: [blogPostTags.postId],
    references: [blogPosts.id],
  }),
  tag: one(blogTags, {
    fields: [blogPostTags.tagId],
    references: [blogTags.id],
  }),
}));

// 博客分类表
export const faqCategoryTable = pgTable("faq_categories", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 100 }).notNull(),
  slug: varchar("slug", { length: 100 }).unique().notNull(),
  description: text("description"),
  sort: integer("sort").default(0),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const faqTable = pgTable("faq", {
  id: serial("id").primaryKey(),
  question: varchar("question", { length: 255 }).notNull(),
  answer: text("answer"),
  sort: integer("sort").default(0),
  status: varchar("status", {
    enum: ["draft", "published", "archived"],
  }).default("draft"), // draft, published, archived
  categoryId: integer("category_id").references(() => faqCategoryTable.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});
