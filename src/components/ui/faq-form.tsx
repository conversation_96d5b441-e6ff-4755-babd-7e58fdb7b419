"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FaqRichTextEditor } from "@/components/ui/faq-rich-text-editor";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Save, Loader2, AlertCircle, HelpCircle } from "lucide-react";
import { Project } from "@/types/project";
import { FaqCategoryType, FaqFormData } from "@/types/faq";

interface FaqFormProps {
  project: Project;
  initialData?: Partial<FaqFormData>;
  mode: "create" | "edit";
  onSubmit: (data: FaqFormData) => Promise<void>;
  saving: boolean;
  error: string | null;
}

export function FaqForm({
  project,
  initialData,
  mode,
  onSubmit,
  saving,
  error,
}: FaqFormProps) {
  const [categories, setCategories] = useState<FaqCategoryType[]>([]);
  const [loading, setLoading] = useState(false);

  // Helper function to convert plain text to Tiptap JSON format
  const convertToTiptapJSON = (text: string): string => {
    if (!text) return JSON.stringify({ type: 'doc', content: [] });

    // Check if it's already valid JSON
    try {
      JSON.parse(text);
      return text; // Already JSON
    } catch {
      // Convert plain text to Tiptap JSON format
      return JSON.stringify({
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: text
              }
            ]
          }
        ]
      });
    }
  };

  const [formData, setFormData] = useState<FaqFormData>({
    question: initialData?.question || "",
    answer: convertToTiptapJSON(initialData?.answer || ""),
    categoryId: initialData?.categoryId,
  });

  const loadCategories = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/content/faq-categories?projectId=${project.id}`
      );
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategories(result.data || []);
        }
      }
    } catch (err) {
      console.error("Load FAQ categories error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCategories();
  }, [project.id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate question
    if (!formData.question.trim()) {
      return;
    }

    // Validate answer - check if it's valid JSON and has content
    let hasAnswerContent = false;
    try {
      if (formData.answer) {
        const parsedAnswer = JSON.parse(formData.answer);
        // Check if the parsed content has any text content
        hasAnswerContent = hasContentInTiptapJSON(parsedAnswer);
      }
    } catch {
      // If it's not valid JSON, treat as empty
      hasAnswerContent = false;
    }

    if (!hasAnswerContent) {
      return;
    }

    await onSubmit(formData);
  };

  // Helper function to check if Tiptap JSON has actual content
  const hasContentInTiptapJSON = (json: any): boolean => {
    if (!json || !json.content) return false;

    const checkContent = (content: any[]): boolean => {
      return content.some(node => {
        if (node.type === 'text' && node.text && node.text.trim()) {
          return true;
        }
        if (node.content && Array.isArray(node.content)) {
          return checkContent(node.content);
        }
        return false;
      });
    };

    return checkContent(json.content);
  };

  const handleInputChange = (
    field: keyof FaqFormData,
    value: string | number | undefined
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="flex items-center gap-2 py-4">
            <AlertCircle className="h-5 w-5 text-destructive" />
            <span className="text-destructive">{error}</span>
          </CardContent>
        </Card>
      )}

      {/* 基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            FAQ基本信息
          </CardTitle>
          <CardDescription>填写FAQ的问题和答案内容</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 分类选择 */}
          <div className="space-y-2">
            <Label htmlFor="category">分类</Label>
            <Select
              value={formData.categoryId?.toString() || "none"}
              onValueChange={(value) =>
                handleInputChange(
                  "categoryId",
                  value && value !== "none" ? Number(value) : undefined
                )
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    加载中...
                  </>
                ) : (
                  <>
                    {categories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id.toString()}
                      >
                        {category.name}
                      </SelectItem>
                    ))}
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
          {/* 问题 */}
          <div className="space-y-2">
            <Label htmlFor="question">问题 *</Label>
            <Input
              id="question"
              value={formData.question}
              onChange={(e) => handleInputChange("question", e.target.value)}
              placeholder="请输入常见问题..."
              required
            />
          </div>

          {/* 答案 */}
          <div className="space-y-2">
            <Label htmlFor="answer">答案 *</Label>
            <FaqRichTextEditor
              content={formData.answer}
              onChange={(content) => handleInputChange("answer", content)}
              placeholder="请输入问题的详细答案..."
            />
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <Card>
        <CardContent className="py-4">
          <div className="flex gap-4">
            <Button
              type="submit"
              disabled={
                saving || !formData.question.trim() || !(() => {
                  try {
                    if (!formData.answer) return false;
                    const parsedAnswer = JSON.parse(formData.answer);
                    return hasContentInTiptapJSON(parsedAnswer);
                  } catch {
                    return false;
                  }
                })()
              }
            >
              {saving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {mode === "create" ? "创建中..." : "更新中..."}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {mode === "create" ? "创建FAQ" : "更新FAQ"}
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
}
