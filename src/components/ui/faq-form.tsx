"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Save, Loader2, AlertCircle, HelpCircle } from "lucide-react";
import { Project } from "@/types/project";
import { FaqCategoryType, FaqFormData } from "@/types/faq";

interface FaqFormProps {
  project: Project;
  initialData?: Partial<FaqFormData>;
  mode: "create" | "edit";
  onSubmit: (data: FaqFormData) => Promise<void>;
  saving: boolean;
  error: string | null;
}

export function FaqForm({
  project,
  initialData,
  mode,
  onSubmit,
  saving,
  error,
}: FaqFormProps) {
  const [categories, setCategories] = useState<FaqCategoryType[]>([]);
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState<FaqFormData>({
    question: initialData?.question || "",
    answer: initialData?.answer || "",
    categoryId: initialData?.categoryId,
  });

  const loadCategories = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/content/faq-categories?projectId=${project.id}`
      );
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategories(result.data || []);
        }
      }
    } catch (err) {
      console.error("Load FAQ categories error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCategories();
  }, [project.id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.question.trim() || !formData.answer.trim()) {
      return;
    }

    await onSubmit(formData);
  };

  const handleInputChange = (
    field: keyof FaqFormData,
    value: string | number | undefined
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="flex items-center gap-2 py-4">
            <AlertCircle className="h-5 w-5 text-destructive" />
            <span className="text-destructive">{error}</span>
          </CardContent>
        </Card>
      )}

      {/* 基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            FAQ基本信息
          </CardTitle>
          <CardDescription>填写FAQ的问题和答案内容</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 分类选择 */}
          <div className="space-y-2">
            <Label htmlFor="category">分类</Label>
            <Select
              onValueChange={(value) =>
                handleInputChange(
                  "categoryId",
                  value && value !== "none" ? Number(value) : undefined
                )
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                {loading ? (
                  <SelectItem value="" disabled>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    加载中...
                  </SelectItem>
                ) : (
                  <>
                    <SelectItem value="none">无分类</SelectItem>
                    {categories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id.toString()}
                      >
                        {category.name}
                      </SelectItem>
                    ))}
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
          {/* 问题 */}
          <div className="space-y-2">
            <Label htmlFor="question">问题 *</Label>
            <Input
              id="question"
              value={formData.question}
              onChange={(e) => handleInputChange("question", e.target.value)}
              placeholder="请输入常见问题..."
              required
            />
          </div>

          {/* 答案 */}
          <div className="space-y-2">
            <Label htmlFor="answer">答案 *</Label>
            <Textarea
              id="answer"
              value={formData.answer}
              onChange={(e) => handleInputChange("answer", e.target.value)}
              placeholder="请输入问题的详细答案..."
              rows={8}
              required
            />
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <Card>
        <CardContent className="py-4">
          <div className="flex gap-4">
            <Button
              type="submit"
              disabled={
                saving || !formData.question.trim() || !formData.answer.trim()
              }
            >
              {saving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {mode === "create" ? "创建中..." : "更新中..."}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {mode === "create" ? "创建FAQ" : "更新FAQ"}
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
}
